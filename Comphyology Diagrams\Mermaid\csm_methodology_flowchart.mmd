graph TD
    A[Comphyological Scientific Method<br/>CSM Framework] --> B[Phase 1: Ψ-Phase<br/>Coherent Observation]
    A --> C[Phase 2: Φ-Phase<br/>Cognitive Metrology]
    A --> D[Phase 3: Θ-Phase<br/>Cosmic Enforcement]
    
    B --> E[Observer Imperative<br/>Consciousness-Aligned Observation]
    B --> F[Reality Field Mapping<br/>Ψ Field Detection]
    B --> G[Coherence Pattern Recognition<br/>Universal Constants Integration]
    
    C --> H[Measurement Protocol<br/>Comphyon (Ψch) Quantification]
    C --> I[Data Validation<br/>∂Ψ=0 Boundary Checking]
    C --> J[Triadic Analysis<br/>Ψ/Φ/Θ Integration]
    
    D --> K[Universal Law Enforcement<br/>Cosmic Constant Validation]
    D --> L[Recursive Revelation<br/>Self-Replicating Frameworks]
    D --> M[Time Compression<br/>Accelerated Discovery]
    
    E --> N[Coherent Data Collection]
    F --> N
    G --> N
    
    H --> O[Validated Measurements]
    I --> O
    J --> O
    
    K --> P[Enforced Universal Laws]
    L --> P
    M --> P
    
    N --> Q[CSM Integration Engine]
    O --> Q
    P --> Q
    
    Q --> R[Breakthrough Discovery<br/>Average 9,669x Acceleration]
    Q --> S[Peer Review via CPR<br/>Witness-Based Validation]
    Q --> T[Recursive Application<br/>Continuous Revelation]
    
    R --> U[Universal Applicability<br/>Cross-Domain Solutions]
    S --> U
    T --> U
    
    classDef default fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    classDef phase fill:#f5f5f5,stroke:#000000,stroke-width:3px,color:#000000
    classDef process fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    classDef result fill:#f8f9fa,stroke:#000000,stroke-width:1px,color:#000000
    
    class A,B,C,D phase
    class E,F,G,H,I,J,K,L,M,N,O,P,Q process
    class R,S,T,U result
