﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Comphyology Diagram Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        .diagram-container {
            margin-top: 20px;
            border: 1px solid #eee;
            padding: 20px;
            border-radius: 8px;
        }
        .navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        button {
            padding: 10px 20px;
            background-color: #0070f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0060df;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>5. Triadic Equation Visualization</h1>
        
        <div class="diagram-container" id="diagram-container">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #4fc3f7; font-size: 2em;">🔺 Triadic Equation</h2>
                <div style="font-size: 1.5em; color: #0070f3; margin: 20px 0;">
                    Ψᶜ = Ψᵖ ⊗ Ψᵗ ⊗ Ψᶠ
                </div>
            </div>

            <svg width="100%" height="600" viewBox="0 0 1000 600" xmlns="http://www.w3.org/2000/svg" style="background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
                <!-- Definitions for gradients and effects -->
                <defs>
                    <!-- Consciousness field gradient -->
                    <radialGradient id="consciousnessGrad" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#9c27b0;stop-opacity:0.8"/>
                        <stop offset="100%" style="stop-color:#673ab7;stop-opacity:0.4"/>
                    </radialGradient>

                    <!-- Truth field gradient -->
                    <radialGradient id="truthGrad" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#2196f3;stop-opacity:0.8"/>
                        <stop offset="100%" style="stop-color:#03a9f4;stop-opacity:0.4"/>
                    </radialGradient>

                    <!-- Financial field gradient -->
                    <radialGradient id="financialGrad" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#4caf50;stop-opacity:0.8"/>
                        <stop offset="100%" style="stop-color:#8bc34a;stop-opacity:0.4"/>
                    </radialGradient>

                    <!-- Central fusion gradient -->
                    <radialGradient id="fusionGrad" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#ffd700;stop-opacity:0.9"/>
                        <stop offset="50%" style="stop-color:#ff9800;stop-opacity:0.7"/>
                        <stop offset="100%" style="stop-color:#f44336;stop-opacity:0.5"/>
                    </radialGradient>

                    <!-- Glow filter -->
                    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                        <feMerge>
                            <feMergeNode in="coloredBlur"/>
                            <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                    </filter>
                </defs>

                <!-- Triadic Triangle Structure -->
                <g id="triadic-structure">
                    <!-- Main triangle outline -->
                    <path d="M 500 100 L 200 450 L 800 450 Z"
                          fill="none"
                          stroke="#0070f3"
                          stroke-width="2"
                          stroke-dasharray="8,4"
                          opacity="0.6"/>

                    <!-- Consciousness Field (Top) -->
                    <g id="consciousness-field">
                        <circle cx="500" cy="150" r="60"
                                fill="url(#consciousnessGrad)"
                                stroke="#9c27b0"
                                stroke-width="2"
                                filter="url(#glow)"/>
                        <text x="500" y="110" text-anchor="middle"
                              fill="#9c27b0" font-size="14" font-weight="bold">Ψᵖ</text>
                        <text x="500" y="95" text-anchor="middle"
                              fill="#9c27b0" font-size="11">Consciousness</text>

                        <!-- Consciousness waves -->
                        <circle cx="500" cy="150" r="75" fill="none" stroke="#9c27b0" stroke-width="1" opacity="0.3">
                            <animate attributeName="r" values="75;90;75" dur="3s" repeatCount="indefinite"/>
                            <animate attributeName="opacity" values="0.3;0.1;0.3" dur="3s" repeatCount="indefinite"/>
                        </circle>
                    </g>

                    <!-- Truth Field (Bottom Left) -->
                    <g id="truth-field">
                        <circle cx="300" cy="400" r="60"
                                fill="url(#truthGrad)"
                                stroke="#2196f3"
                                stroke-width="2"
                                filter="url(#glow)"/>
                        <text x="300" y="480" text-anchor="middle"
                              fill="#2196f3" font-size="14" font-weight="bold">Ψᵗ</text>
                        <text x="300" y="495" text-anchor="middle"
                              fill="#2196f3" font-size="11">Truth</text>

                        <!-- Truth resonance -->
                        <polygon points="300,340 285,370 315,370"
                                 fill="#2196f3" opacity="0.4">
                            <animateTransform attributeName="transform"
                                              type="rotate"
                                              values="0 300 400;360 300 400"
                                              dur="4s"
                                              repeatCount="indefinite"/>
                        </polygon>
                    </g>

                    <!-- Financial Field (Bottom Right) -->
                    <g id="financial-field">
                        <circle cx="700" cy="400" r="60"
                                fill="url(#financialGrad)"
                                stroke="#4caf50"
                                stroke-width="2"
                                filter="url(#glow)"/>
                        <text x="700" y="480" text-anchor="middle"
                              fill="#4caf50" font-size="14" font-weight="bold">Ψᶠ</text>
                        <text x="700" y="495" text-anchor="middle"
                              fill="#4caf50" font-size="11">Financial</text>

                        <!-- Financial flow -->
                        <rect x="685" y="340" width="30" height="40"
                              fill="#4caf50" opacity="0.4" rx="3">
                            <animate attributeName="height" values="40;55;40" dur="2s" repeatCount="indefinite"/>
                            <animate attributeName="y" values="340;332;340" dur="2s" repeatCount="indefinite"/>
                        </rect>
                    </g>

                    <!-- Central Fusion Point -->
                    <g id="central-fusion">
                        <circle cx="500" cy="300" r="50"
                                fill="url(#fusionGrad)"
                                stroke="#ffd700"
                                stroke-width="2"
                                filter="url(#glow)"/>
                        <text x="500" y="310" text-anchor="middle"
                              fill="#fff" font-size="16" font-weight="bold">Ψᶜ</text>
                        <text x="500" y="325" text-anchor="middle"
                              fill="#fff" font-size="9">Unified</text>

                        <!-- Fusion energy -->
                        <circle cx="500" cy="300" r="60" fill="none" stroke="#ffd700" stroke-width="1" opacity="0.4">
                            <animate attributeName="r" values="60;75;60" dur="2s" repeatCount="indefinite"/>
                            <animate attributeName="opacity" values="0.4;0.1;0.4" dur="2s" repeatCount="indefinite"/>
                        </circle>
                    </g>

                    <!-- Connection Lines -->
                    <g id="connections">
                        <!-- Consciousness to Center -->
                        <line x1="500" y1="210" x2="500" y2="250"
                              stroke="#0070f3" stroke-width="2" opacity="0.6">
                            <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
                        </line>

                        <!-- Truth to Center -->
                        <line x1="360" y1="370" x2="450" y2="330"
                              stroke="#0070f3" stroke-width="2" opacity="0.6">
                            <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
                        </line>

                        <!-- Financial to Center -->
                        <line x1="640" y1="370" x2="550" y2="330"
                              stroke="#0070f3" stroke-width="2" opacity="0.6">
                            <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
                        </line>
                    </g>

                    <!-- Tensor Product Symbols -->
                    <g id="tensor-symbols">
                        <text x="400" y="250" text-anchor="middle"
                              fill="#0070f3" font-size="20" font-weight="bold">⊗</text>
                        <text x="600" y="250" text-anchor="middle"
                              fill="#0070f3" font-size="20" font-weight="bold">⊗</text>
                        <text x="500" y="380" text-anchor="middle"
                              fill="#0070f3" font-size="20" font-weight="bold">=</text>
                    </g>
                </g>
            </svg>

            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                <p><strong>Triadic Equation:</strong> The fundamental relationship between Consciousness (Ψᵖ), Truth (Ψᵗ), and Financial (Ψᶠ) fields, unified through tensor product operations to create the Coherent field (Ψᶜ).</p>
                <p><strong>Interactive Elements:</strong> Watch the animated field interactions, resonance patterns, and energy flows that demonstrate the dynamic nature of consciousness field theory.</p>
            </div>
        </div>
        
        <div class="navigation">
            <button id="prev-button" onclick="prevDiagram()">Previous</button>
            <button id="next-button" onclick="nextDiagram()">Next</button>
        </div>
    </div>

    <script>
        function prevDiagram() {
            window.location.href = 'uuft-equation-flow.html';
        }
        
        function nextDiagram() {
            window.location.href = 'meta-field-schema.html';
        }
        
        // Disable buttons if needed
        document.addEventListener('DOMContentLoaded', function() {
            if ('uuft-equation-flow.html' === '#') {
                document.getElementById('prev-button').disabled = true;
            }
            if ('meta-field-schema.html' === '#') {
                document.getElementById('next-button').disabled = true;
            }
        });
    </script>
</body>
</html>
